# === Node.js ===
node_modules/
npm-debug.log*

# === Next.js ===
.next/
out/              
.vercel/             

# === Env files ===
.env*
!.env.example

# === TypeScript ===
*.tsbuildinfo

# === Logs ===
logs/
*.log

# === OS and editor ===
.DS_Store
Thumbs.db
.vscode/

# === Build tools ===
dist/
build/

# === Misc ===
coverage/
*.local

# === Optional: lockfiles ===
# Only include one if you're using a specific package manager
package-lock.json

gcp-key.json
